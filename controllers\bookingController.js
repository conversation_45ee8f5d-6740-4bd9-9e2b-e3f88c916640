const pb = require('../pocketbase/pbClient');

exports.createBooking = async (req, res) => {
  try {
    const data = req.body;

    // Validasi transportasi
    if (data.transport === 'Yes' && (!data.transport_type || data.transport_type.trim() === '')) {
      return res.status(400).json({ message: 'Harus memilih tipe transportasi jika memilih Yes' });
    }

    if (data.transport === 'No' && (!data.driver_number || data.driver_number.trim() === '')) {
      return res.status(400).json({ message: 'Harus mengisi nomor sopir jika tidak memilih transportasi' });
    }

    // Simpan booking
    const booking = await pb.collection('booking_uab').create({
      ...data,
      user_id: req.user.id
    });

    res.status(201).json(booking);
  } catch (err) {
    res.status(400).json({
      message: "Failed to create record.",
      error: err?.response?.data || err.message || err
    });
  }
};
